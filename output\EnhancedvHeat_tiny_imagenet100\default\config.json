AMP_ENABLE: true
AMP_OPT_LEVEL: ''
AUG:
  AUTO_AUGMENT: rand-m9-mstd0.5-inc1
  COLOR_JITTER: 0.4
  CUTMIX: 1.0
  CUTMIX_MINMAX: null
  MIXUP: 0.8
  MIXUP_MODE: batch
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  RECOUNT: 1
  REMODE: pixel
  REPROB: 0.25
BASE:
- ''
DATA:
  BATCH_SIZE: 256
  CACHE_MODE: part
  DATASET: imagenet
  DATA_PATH: /root/lanyun-fs/imagenet100-split
  IMG_SIZE: 224
  INTERPOLATION: bicubic
  NUM_WORKERS: 8
  PIN_MEMORY: true
  ZIP_MODE: false
ENABLE_AMP: false
EVAL_MODE: false
FUSED_LAYERNORM: false
FUSED_WINDOW_PROCESS: false
LOCAL_RANK: 0
MODEL:
  DROP_PATH_RATE: 0.1
  DROP_RATE: 0.0
  LABEL_SMOOTHING: 0.1
  NAME: EnhancedvHeat_tiny_imagenet100
  NUM_CLASSES: 100
  PRETRAINED: ''
  RESUME: ''
  TYPE: EnhancedvHeat
  VHEAT:
    DEPTHS:
    - 2
    - 2
    - 6
    - 2
    EMBED_DIM: 96
    ENABLE_THERMAL_RADIATION: true
    IN_CHANS: 3
    LAYER_SCALE: null
    MLP_RATIO: 4.0
    PATCH_NORM: true
    PATCH_SIZE: 4
    POST_NORM: false
    RADIATION_STRENGTH: 0.05
OUTPUT: ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default
PRINT_FREQ: 10
SAVE_FREQ: 5
SEED: 0
TAG: default
TEST:
  CROP: true
  SEQUENTIAL: false
  SHUFFLE: false
THROUGHPUT_MODE: false
TRAIN:
  ACCUMULATION_STEPS: 1
  AUTO_RESUME: true
  BASE_LR: 0.00025
  CLIP_GRAD: 5.0
  EPOCHS: 100
  LAYER_DECAY: 1.0
  LR_SCHEDULER:
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    GAMMA: 0.1
    MULTISTEPS: []
    NAME: cosine
    WARMUP_PREFIX: true
  MIN_LR: 2.5e-06
  MOE:
    SAVE_MASTER: false
  OPTIMIZER:
    BETAS:
    - 0.9
    - 0.999
    EPS: 1.0e-08
    MOMENTUM: 0.9
    NAME: adamw
  START_EPOCH: 0
  USE_CHECKPOINT: false
  WARMUP_EPOCHS: 20
  WARMUP_LR: 2.5e-07
  WEIGHT_DECAY: 0.08
