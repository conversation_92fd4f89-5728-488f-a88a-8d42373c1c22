[2025-08-26 20:40:35 EnhancedvHeat_tiny_imagenet100] (main.py 467): INFO Full config saved to ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default/config.json
[2025-08-26 20:40:35 EnhancedvHeat_tiny_imagenet100] (main.py 470): INFO AMP_ENABLE: true
AMP_OPT_LEVEL: ''
AUG:
  AUTO_AUGMENT: rand-m9-mstd0.5-inc1
  COLOR_JITTER: 0.4
  CUTMIX: 1.0
  CUTMIX_MINMAX: null
  MIXUP: 0.8
  MIXUP_MODE: batch
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  RECOUNT: 1
  REMODE: pixel
  REPROB: 0.25
BASE:
- ''
DATA:
  BATCH_SIZE: 256
  CACHE_MODE: part
  DATASET: imagenet
  DATA_PATH: /root/lanyun-fs/imagenet100-split
  IMG_SIZE: 224
  INTERPOLATION: bicubic
  NUM_WORKERS: 8
  PIN_MEMORY: true
  ZIP_MODE: false
ENABLE_AMP: false
EVAL_MODE: false
FUSED_LAYERNORM: false
FUSED_WINDOW_PROCESS: false
LOCAL_RANK: 0
MODEL:
  DROP_PATH_RATE: 0.1
  DROP_RATE: 0.0
  LABEL_SMOOTHING: 0.1
  NAME: EnhancedvHeat_tiny_imagenet100
  NUM_CLASSES: 100
  PRETRAINED: ''
  RESUME: ''
  TYPE: EnhancedvHeat
  VHEAT:
    DEPTHS:
    - 2
    - 2
    - 6
    - 2
    EMBED_DIM: 96
    ENABLE_THERMAL_RADIATION: true
    IN_CHANS: 3
    LAYER_SCALE: null
    MLP_RATIO: 4.0
    PATCH_NORM: true
    PATCH_SIZE: 4
    POST_NORM: false
    RADIATION_STRENGTH: 0.05
OUTPUT: ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default
PRINT_FREQ: 10
SAVE_FREQ: 5
SEED: 0
TAG: default
TEST:
  CROP: true
  SEQUENTIAL: false
  SHUFFLE: false
THROUGHPUT_MODE: false
TRAIN:
  ACCUMULATION_STEPS: 1
  AUTO_RESUME: true
  BASE_LR: 0.00025
  CLIP_GRAD: 5.0
  EPOCHS: 100
  LAYER_DECAY: 1.0
  LR_SCHEDULER:
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    GAMMA: 0.1
    MULTISTEPS: []
    NAME: cosine
    WARMUP_PREFIX: true
  MIN_LR: 2.5e-06
  MOE:
    SAVE_MASTER: false
  OPTIMIZER:
    BETAS:
    - 0.9
    - 0.999
    EPS: 1.0e-08
    MOMENTUM: 0.9
    NAME: adamw
  START_EPOCH: 0
  USE_CHECKPOINT: false
  WARMUP_EPOCHS: 20
  WARMUP_LR: 2.5e-07
  WEIGHT_DECAY: 0.08

[2025-08-26 20:40:35 EnhancedvHeat_tiny_imagenet100] (main.py 471): INFO {"cfg": "classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml", "opts": ["TRAIN.EPOCHS", "100", "MODEL.NUM_CLASSES", "100"], "batch_size": 256, "data_path": "/root/lanyun-fs/imagenet100-split", "zip": false, "cache_mode": "part", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "./output_enhanced_tiny", "tag": null, "eval": false, "throughput": false, "local_rank": 0, "fused_layernorm": false, "optim": null, "model_ema": true, "model_ema_decay": 0.9999, "model_ema_force_cpu": false}
[2025-08-26 20:40:36 EnhancedvHeat_tiny_imagenet100] (main.py 117): INFO Creating model:EnhancedvHeat/EnhancedvHeat_tiny_imagenet100
[2025-08-26 20:40:38 EnhancedvHeat_tiny_imagenet100] (main.py 124): INFO EnhancedvHeat(
  (patch_embed): StemLayer(
    (conv1): Conv2d(3, 48, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
    (norm1): Sequential(
      (0): to_channels_last()
      (1): LayerNorm((48,), eps=1e-06, elementwise_affine=True)
      (2): to_channels_first()
    )
    (act): GELU(approximate='none')
    (conv2): Conv2d(48, 96, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1))
    (norm2): Sequential(
      (0): to_channels_last()
      (1): LayerNorm((96,), eps=1e-06, elementwise_affine=True)
      (2): to_channels_first()
    )
  )
  (freq_embed): ParameterList(
      (0): Parameter containing: [torch.float32 of size 56x56x96]
      (1): Parameter containing: [torch.float32 of size 28x28x192]
      (2): Parameter containing: [torch.float32 of size 14x14x384]
      (3): Parameter containing: [torch.float32 of size 7x7x768]
  )
  (layers): ModuleList(
    (0): AdditionalInputSequential(
      (0): EnhancedHeatBlock(
        (norm1): LayerNorm2d((96,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=96)
          (linear): Linear(in_features=96, out_features=192, bias=True)
          (out_norm): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=96, out_features=96, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=96, out_features=96, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=96, out_features=96, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(96, 24, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(24, 96, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): Identity()
        (norm2): LayerNorm2d((96,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(96, 384, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(384, 96, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        (norm1): LayerNorm2d((96,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=96)
          (linear): Linear(in_features=96, out_features=192, bias=True)
          (out_norm): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=96, out_features=96, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=96, out_features=96, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=96, out_features=96, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(96, 24, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(24, 96, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.00909090880304575)
        (norm2): LayerNorm2d((96,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(96, 384, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(384, 96, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): Sequential(
        (0): Conv2d(96, 192, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (1): LayerNorm2d((192,), eps=1e-05, elementwise_affine=True)
      )
    )
    (1): AdditionalInputSequential(
      (0): EnhancedHeatBlock(
        (norm1): LayerNorm2d((192,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192)
          (linear): Linear(in_features=192, out_features=384, bias=True)
          (out_norm): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=192, out_features=192, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=192, out_features=192, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=192, out_features=192, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(192, 48, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(48, 192, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.0181818176060915)
        (norm2): LayerNorm2d((192,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(192, 768, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(768, 192, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        (norm1): LayerNorm2d((192,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192)
          (linear): Linear(in_features=192, out_features=384, bias=True)
          (out_norm): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=192, out_features=192, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=192, out_features=192, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=192, out_features=192, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(192, 48, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(48, 192, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.027272727340459824)
        (norm2): LayerNorm2d((192,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(192, 768, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(768, 192, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): Sequential(
        (0): Conv2d(192, 384, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (1): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
      )
    )
    (2): AdditionalInputSequential(
      (0): EnhancedHeatBlock(
        (norm1): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=384, out_features=384, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(384, 96, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(96, 384, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.036363635212183)
        (norm2): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(384, 1536, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(1536, 384, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        (norm1): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=384, out_features=384, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(384, 96, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(96, 384, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.045454543083906174)
        (norm2): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(384, 1536, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(1536, 384, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): EnhancedHeatBlock(
        (norm1): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=384, out_features=384, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(384, 96, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(96, 384, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.054545458406209946)
        (norm2): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(384, 1536, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(1536, 384, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (3): EnhancedHeatBlock(
        (norm1): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=384, out_features=384, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(384, 96, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(96, 384, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.06363636255264282)
        (norm2): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(384, 1536, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(1536, 384, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (4): EnhancedHeatBlock(
        (norm1): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=384, out_features=384, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(384, 96, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(96, 384, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.0727272778749466)
        (norm2): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(384, 1536, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(1536, 384, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (5): EnhancedHeatBlock(
        (norm1): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=384, out_features=384, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(384, 96, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(96, 384, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.08181818574666977)
        (norm2): LayerNorm2d((384,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(384, 1536, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(1536, 384, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (6): Sequential(
        (0): Conv2d(384, 768, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (1): LayerNorm2d((768,), eps=1e-05, elementwise_affine=True)
      )
    )
    (3): AdditionalInputSequential(
      (0): EnhancedHeatBlock(
        (norm1): LayerNorm2d((768,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=768)
          (linear): Linear(in_features=768, out_features=1536, bias=True)
          (out_norm): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=768, out_features=768, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=768, out_features=768, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=768, out_features=768, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(768, 192, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(192, 768, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.09090909361839294)
        (norm2): LayerNorm2d((768,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(768, 3072, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(3072, 768, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        (norm1): LayerNorm2d((768,), eps=1e-05, elementwise_affine=True)
        (spatial_heat_conduction): Heat2D(
          (dwconv): Conv2d(768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=768)
          (linear): Linear(in_features=768, out_features=1536, bias=True)
          (out_norm): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=768, out_features=768, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=768, out_features=768, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          (channel_interaction): Linear(in_features=768, out_features=768, bias=False)
          (temperature_predictor): Sequential(
            (0): AdaptiveAvgPool2d(output_size=1)
            (1): Conv2d(768, 192, kernel_size=(1, 1), stride=(1, 1))
            (2): ReLU()
            (3): Conv2d(192, 768, kernel_size=(1, 1), stride=(1, 1))
            (4): Sigmoid()
          )
        )
        (drop_path): DropPath(0.10000000149011612)
        (norm2): LayerNorm2d((768,), eps=1e-05, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Conv2d(768, 3072, kernel_size=(1, 1), stride=(1, 1))
          (act): GELU(approximate='none')
          (fc2): Conv2d(3072, 768, kernel_size=(1, 1), stride=(1, 1))
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): Identity()
    )
  )
  (classifier): Sequential(
    (0): LayerNorm2d((768,), eps=1e-05, elementwise_affine=True)
    (1): AdaptiveAvgPool2d(output_size=1)
    (2): Flatten(start_dim=1, end_dim=-1)
    (3): Linear(in_features=768, out_features=100, bias=True)
  )
)
[2025-08-26 20:40:38 EnhancedvHeat_tiny_imagenet100] (main.py 126): INFO number of params: 33406132
[2025-08-26 20:40:39 EnhancedvHeat_tiny_imagenet100] (main.py 175): INFO no checkpoint found in ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default, ignoring auto resume
[2025-08-26 20:40:39 EnhancedvHeat_tiny_imagenet100] (main.py 215): INFO Start training
[2025-08-26 20:45:05 EnhancedvHeat_tiny_imagenet100] (main.py 467): INFO Full config saved to ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default/config.json
[2025-08-26 20:45:05 EnhancedvHeat_tiny_imagenet100] (main.py 470): INFO AMP_ENABLE: true
AMP_OPT_LEVEL: ''
AUG:
  AUTO_AUGMENT: rand-m9-mstd0.5-inc1
  COLOR_JITTER: 0.4
  CUTMIX: 1.0
  CUTMIX_MINMAX: null
  MIXUP: 0.8
  MIXUP_MODE: batch
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  RECOUNT: 1
  REMODE: pixel
  REPROB: 0.25
BASE:
- ''
DATA:
  BATCH_SIZE: 256
  CACHE_MODE: part
  DATASET: imagenet
  DATA_PATH: /root/lanyun-fs/imagenet100-split
  IMG_SIZE: 224
  INTERPOLATION: bicubic
  NUM_WORKERS: 8
  PIN_MEMORY: true
  ZIP_MODE: false
ENABLE_AMP: false
EVAL_MODE: false
FUSED_LAYERNORM: false
FUSED_WINDOW_PROCESS: false
LOCAL_RANK: 0
MODEL:
  DROP_PATH_RATE: 0.1
  DROP_RATE: 0.0
  LABEL_SMOOTHING: 0.1
  NAME: EnhancedvHeat_tiny_imagenet100
  NUM_CLASSES: 100
  PRETRAINED: ''
  RESUME: ''
  TYPE: EnhancedvHeat
  VHEAT:
    DEPTHS:
    - 2
    - 2
    - 6
    - 2
    EMBED_DIM: 96
    ENABLE_THERMAL_RADIATION: true
    IN_CHANS: 3
    LAYER_SCALE: null
    MLP_RATIO: 4.0
    PATCH_NORM: true
    PATCH_SIZE: 4
    POST_NORM: false
    RADIATION_STRENGTH: 0.05
OUTPUT: ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default
PRINT_FREQ: 10
SAVE_FREQ: 5
SEED: 0
TAG: default
TEST:
  CROP: true
  SEQUENTIAL: false
  SHUFFLE: false
THROUGHPUT_MODE: false
TRAIN:
  ACCUMULATION_STEPS: 1
  AUTO_RESUME: true
  BASE_LR: 0.00025
  CLIP_GRAD: 5.0
  EPOCHS: 100
  LAYER_DECAY: 1.0
  LR_SCHEDULER:
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    GAMMA: 0.1
    MULTISTEPS: []
    NAME: cosine
    WARMUP_PREFIX: true
  MIN_LR: 2.5e-06
  MOE:
    SAVE_MASTER: false
  OPTIMIZER:
    BETAS:
    - 0.9
    - 0.999
    EPS: 1.0e-08
    MOMENTUM: 0.9
    NAME: adamw
  START_EPOCH: 0
  USE_CHECKPOINT: false
  WARMUP_EPOCHS: 20
  WARMUP_LR: 2.5e-07
  WEIGHT_DECAY: 0.08

[2025-08-26 20:45:05 EnhancedvHeat_tiny_imagenet100] (main.py 471): INFO {"cfg": "classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml", "opts": ["TRAIN.EPOCHS", "100", "MODEL.NUM_CLASSES", "100"], "batch_size": 256, "data_path": "/root/lanyun-fs/imagenet100-split", "zip": false, "cache_mode": "part", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "./output_enhanced_tiny", "tag": null, "eval": false, "throughput": false, "local_rank": 0, "fused_layernorm": false, "optim": null, "model_ema": true, "model_ema_decay": 0.9999, "model_ema_force_cpu": false}
[2025-08-26 20:45:05 EnhancedvHeat_tiny_imagenet100] (main.py 117): INFO Creating model:EnhancedvHeat/EnhancedvHeat_tiny_imagenet100
[2025-08-26 20:45:14 EnhancedvHeat_tiny_imagenet100] (main.py 122): INFO N/A indicates a possibly missing statistic due to how the module was called. Missing values are still included in the parent's total.
EnhancedvHeat(
  #params: 33.41M, #flops: 4.94G
  (patch_embed): StemLayer(
    #params: 43.2K, #flops: 0.15G
    (conv1): Conv2d(
      3, 48, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1)
      #params: 1.34K, #flops: 16.26M
    )
    (norm1): Sequential(
      #params: 96, #flops: 3.01M
      (0): to_channels_last()
      (1): LayerNorm(
        (48,), eps=1e-06, elementwise_affine=True
        #params: 96, #flops: 3.01M
      )
      (2): to_channels_first()
    )
    (act): GELU(approximate='none')
    (conv2): Conv2d(
      48, 96, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1)
      #params: 41.57K, #flops: 0.13G
    )
    (norm2): Sequential(
      #params: 0.19K, #flops: 1.51M
      (0): to_channels_last()
      (1): LayerNorm(
        (96,), eps=1e-06, elementwise_affine=True
        #params: 0.19K, #flops: 1.51M
      )
      (2): to_channels_first()
    )
  )
  (freq_embed): ParameterList(
      (0): Parameter containing: [torch.float32 of size 56x56x96]
      (1): Parameter containing: [torch.float32 of size 28x28x192]
      (2): Parameter containing: [torch.float32 of size 14x14x384]
      (3): Parameter containing: [torch.float32 of size 7x7x768]
    #params: 0.56M, #flops: N/A
  )
  (layers): ModuleList(
    #params: 32.72M, #flops: 4.78G
    (0): AdditionalInputSequential(
      #params: 0.42M, #flops: 0.97G
      (0): EnhancedHeatBlock(
        #params: 0.13M, #flops: 0.42G
        (norm1): LayerNorm2d(
          (96,), eps=1e-05, elementwise_affine=True
          #params: 0.19K, #flops: 1.51M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 38.4K, #flops: 0.19G
          (dwconv): Conv2d(
            96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=96
            #params: 0.96K, #flops: 2.71M
          )
          (linear): Linear(
            in_features=96, out_features=192, bias=True
            #params: 18.62K, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (96,), eps=1e-05, elementwise_affine=True
            #params: 0.19K, #flops: 1.51M
          )
          (out_linear): Linear(
            in_features=96, out_features=96, bias=True
            #params: 9.31K, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 9.31K, #flops: 28.9M
            (0): Linear(
              in_features=96, out_features=96, bias=True
              #params: 9.31K, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=96)
          (linear): Linear(in_features=96, out_features=192, bias=True)
          (out_norm): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=96, out_features=96, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=96, out_features=96, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 14.04K, #flops: N/A
          (channel_interaction): Linear(
            in_features=96, out_features=96, bias=False
            #params: 9.22K, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 4.73K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              96, 24, kernel_size=(1, 1), stride=(1, 1)
              #params: 2.33K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              24, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 2.4K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): Identity(#params: 0, #flops: N/A)
        (norm2): LayerNorm2d(
          (96,), eps=1e-05, elementwise_affine=True
          #params: 0.19K, #flops: 1.51M
        )
        (mlp): Mlp(
          #params: 74.21K, #flops: 0.23G
          (fc1): Conv2d(
            96, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 37.25K, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            384, 96, kernel_size=(1, 1), stride=(1, 1)
            #params: 36.96K, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        #params: 0.13M, #flops: 0.42G
        (norm1): LayerNorm2d(
          (96,), eps=1e-05, elementwise_affine=True
          #params: 0.19K, #flops: 1.51M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 38.4K, #flops: 0.19G
          (dwconv): Conv2d(
            96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=96
            #params: 0.96K, #flops: 2.71M
          )
          (linear): Linear(
            in_features=96, out_features=192, bias=True
            #params: 18.62K, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (96,), eps=1e-05, elementwise_affine=True
            #params: 0.19K, #flops: 1.51M
          )
          (out_linear): Linear(
            in_features=96, out_features=96, bias=True
            #params: 9.31K, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 9.31K, #flops: 28.9M
            (0): Linear(
              in_features=96, out_features=96, bias=True
              #params: 9.31K, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=96)
          (linear): Linear(in_features=96, out_features=192, bias=True)
          (out_norm): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=96, out_features=96, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=96, out_features=96, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 14.04K, #flops: N/A
          (channel_interaction): Linear(
            in_features=96, out_features=96, bias=False
            #params: 9.22K, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 4.73K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              96, 24, kernel_size=(1, 1), stride=(1, 1)
              #params: 2.33K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              24, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 2.4K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (96,), eps=1e-05, elementwise_affine=True
          #params: 0.19K, #flops: 1.51M
        )
        (mlp): Mlp(
          #params: 74.21K, #flops: 0.23G
          (fc1): Conv2d(
            96, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 37.25K, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            384, 96, kernel_size=(1, 1), stride=(1, 1)
            #params: 36.96K, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): Sequential(
        #params: 0.17M, #flops: 0.13G
        (0): Conv2d(
          96, 192, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False
          #params: 0.17M, #flops: 0.13G
        )
        (1): LayerNorm2d(
          (192,), eps=1e-05, elementwise_affine=True
          #params: 0.38K, #flops: 0.75M
        )
      )
    )
    (1): AdditionalInputSequential(
      #params: 1.67M, #flops: 0.87G
      (0): EnhancedHeatBlock(
        #params: 0.5M, #flops: 0.37G
        (norm1): LayerNorm2d(
          (192,), eps=1e-05, elementwise_affine=True
          #params: 0.38K, #flops: 0.75M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.15M, #flops: 0.13G
          (dwconv): Conv2d(
            192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192
            #params: 1.92K, #flops: 1.35M
          )
          (linear): Linear(
            in_features=192, out_features=384, bias=True
            #params: 74.11K, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (192,), eps=1e-05, elementwise_affine=True
            #params: 0.38K, #flops: 0.75M
          )
          (out_linear): Linear(
            in_features=192, out_features=192, bias=True
            #params: 37.06K, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 37.06K, #flops: 28.9M
            (0): Linear(
              in_features=192, out_features=192, bias=True
              #params: 37.06K, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192)
          (linear): Linear(in_features=192, out_features=384, bias=True)
          (out_norm): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=192, out_features=192, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=192, out_features=192, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 55.73K, #flops: N/A
          (channel_interaction): Linear(
            in_features=192, out_features=192, bias=False
            #params: 36.86K, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 18.67K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              192, 48, kernel_size=(1, 1), stride=(1, 1)
              #params: 9.26K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              48, 192, kernel_size=(1, 1), stride=(1, 1)
              #params: 9.41K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (192,), eps=1e-05, elementwise_affine=True
          #params: 0.38K, #flops: 0.75M
        )
        (mlp): Mlp(
          #params: 0.3M, #flops: 0.23G
          (fc1): Conv2d(
            192, 768, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.15M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            768, 192, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.15M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        #params: 0.5M, #flops: 0.37G
        (norm1): LayerNorm2d(
          (192,), eps=1e-05, elementwise_affine=True
          #params: 0.38K, #flops: 0.75M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.15M, #flops: 0.13G
          (dwconv): Conv2d(
            192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192
            #params: 1.92K, #flops: 1.35M
          )
          (linear): Linear(
            in_features=192, out_features=384, bias=True
            #params: 74.11K, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (192,), eps=1e-05, elementwise_affine=True
            #params: 0.38K, #flops: 0.75M
          )
          (out_linear): Linear(
            in_features=192, out_features=192, bias=True
            #params: 37.06K, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 37.06K, #flops: 28.9M
            (0): Linear(
              in_features=192, out_features=192, bias=True
              #params: 37.06K, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192)
          (linear): Linear(in_features=192, out_features=384, bias=True)
          (out_norm): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=192, out_features=192, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=192, out_features=192, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 55.73K, #flops: N/A
          (channel_interaction): Linear(
            in_features=192, out_features=192, bias=False
            #params: 36.86K, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 18.67K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              192, 48, kernel_size=(1, 1), stride=(1, 1)
              #params: 9.26K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              48, 192, kernel_size=(1, 1), stride=(1, 1)
              #params: 9.41K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (192,), eps=1e-05, elementwise_affine=True
          #params: 0.38K, #flops: 0.75M
        )
        (mlp): Mlp(
          #params: 0.3M, #flops: 0.23G
          (fc1): Conv2d(
            192, 768, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.15M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            768, 192, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.15M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): Sequential(
        #params: 0.66M, #flops: 0.13G
        (0): Conv2d(
          192, 384, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False
          #params: 0.66M, #flops: 0.13G
        )
        (1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
      )
    )
    (2): AdditionalInputSequential(
      #params: 14.66M, #flops: 2.25G
      (0): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (3): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (4): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (5): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (6): Sequential(
        #params: 2.66M, #flops: 0.13G
        (0): Conv2d(
          384, 768, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False
          #params: 2.65M, #flops: 0.13G
        )
        (1): LayerNorm2d(
          (768,), eps=1e-05, elementwise_affine=True
          #params: 1.54K, #flops: 0.19M
        )
      )
    )
    (3): AdditionalInputSequential(
      #params: 15.97M, #flops: 0.7G
      (0): EnhancedHeatBlock(
        #params: 7.98M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (768,), eps=1e-05, elementwise_affine=True
          #params: 1.54K, #flops: 0.19M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 2.37M, #flops: 0.12G
          (dwconv): Conv2d(
            768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=768
            #params: 7.68K, #flops: 0.34M
          )
          (linear): Linear(
            in_features=768, out_features=1536, bias=True
            #params: 1.18M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (768,), eps=1e-05, elementwise_affine=True
            #params: 1.54K, #flops: 0.19M
          )
          (out_linear): Linear(
            in_features=768, out_features=768, bias=True
            #params: 0.59M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.59M, #flops: 28.9M
            (0): Linear(
              in_features=768, out_features=768, bias=True
              #params: 0.59M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=768)
          (linear): Linear(in_features=768, out_features=1536, bias=True)
          (out_norm): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=768, out_features=768, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=768, out_features=768, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.89M, #flops: N/A
          (channel_interaction): Linear(
            in_features=768, out_features=768, bias=False
            #params: 0.59M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 0.3M, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              768, 192, kernel_size=(1, 1), stride=(1, 1)
              #params: 0.15M, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              192, 768, kernel_size=(1, 1), stride=(1, 1)
              #params: 0.15M, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (768,), eps=1e-05, elementwise_affine=True
          #params: 1.54K, #flops: 0.19M
        )
        (mlp): Mlp(
          #params: 4.72M, #flops: 0.23G
          (fc1): Conv2d(
            768, 3072, kernel_size=(1, 1), stride=(1, 1)
            #params: 2.36M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            3072, 768, kernel_size=(1, 1), stride=(1, 1)
            #params: 2.36M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        #params: 7.98M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (768,), eps=1e-05, elementwise_affine=True
          #params: 1.54K, #flops: 0.19M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 2.37M, #flops: 0.12G
          (dwconv): Conv2d(
            768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=768
            #params: 7.68K, #flops: 0.34M
          )
          (linear): Linear(
            in_features=768, out_features=1536, bias=True
            #params: 1.18M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (768,), eps=1e-05, elementwise_affine=True
            #params: 1.54K, #flops: 0.19M
          )
          (out_linear): Linear(
            in_features=768, out_features=768, bias=True
            #params: 0.59M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.59M, #flops: 28.9M
            (0): Linear(
              in_features=768, out_features=768, bias=True
              #params: 0.59M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=768)
          (linear): Linear(in_features=768, out_features=1536, bias=True)
          (out_norm): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=768, out_features=768, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=768, out_features=768, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.89M, #flops: N/A
          (channel_interaction): Linear(
            in_features=768, out_features=768, bias=False
            #params: 0.59M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 0.3M, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              768, 192, kernel_size=(1, 1), stride=(1, 1)
              #params: 0.15M, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              192, 768, kernel_size=(1, 1), stride=(1, 1)
              #params: 0.15M, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (768,), eps=1e-05, elementwise_affine=True
          #params: 1.54K, #flops: 0.19M
        )
        (mlp): Mlp(
          #params: 4.72M, #flops: 0.23G
          (fc1): Conv2d(
            768, 3072, kernel_size=(1, 1), stride=(1, 1)
            #params: 2.36M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            3072, 768, kernel_size=(1, 1), stride=(1, 1)
            #params: 2.36M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): Identity(#params: 0, #flops: N/A)
    )
  )
  (classifier): Sequential(
    #params: 78.44K, #flops: 0.3M
    (0): LayerNorm2d(
      (768,), eps=1e-05, elementwise_affine=True
      #params: 1.54K, #flops: 0.19M
    )
    (1): AdaptiveAvgPool2d(
      output_size=1
      #params: 0, #flops: 37.63K
    )
    (2): Flatten(start_dim=1, end_dim=-1)
    (3): Linear(
      in_features=768, out_features=100, bias=True
      #params: 76.9K, #flops: 76.8K
    )
  )
)
[2025-08-26 20:45:14 EnhancedvHeat_tiny_imagenet100] (main.py 175): INFO no checkpoint found in ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default, ignoring auto resume
[2025-08-26 20:45:14 EnhancedvHeat_tiny_imagenet100] (main.py 215): INFO Start training
[2025-08-26 20:45:25 EnhancedvHeat_tiny_imagenet100] (main.py 317): INFO Train: [0/100][0/507]	eta 1:32:28 lr 0.000000	 wd 0.0800	time 10.9443 (10.9443)	data time 5.0832 (5.0832)	loss 4.6753 (4.6753)	grad_norm 3.4124 (3.4124)	loss_scale 65536.0000 (65536.0000)	mem 22937MB
[2025-08-26 20:47:56 EnhancedvHeat_tiny_imagenet100] (main.py 467): INFO Full config saved to ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default/config.json
[2025-08-26 20:47:56 EnhancedvHeat_tiny_imagenet100] (main.py 470): INFO AMP_ENABLE: true
AMP_OPT_LEVEL: ''
AUG:
  AUTO_AUGMENT: rand-m9-mstd0.5-inc1
  COLOR_JITTER: 0.4
  CUTMIX: 1.0
  CUTMIX_MINMAX: null
  MIXUP: 0.8
  MIXUP_MODE: batch
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  RECOUNT: 1
  REMODE: pixel
  REPROB: 0.25
BASE:
- ''
DATA:
  BATCH_SIZE: 256
  CACHE_MODE: part
  DATASET: imagenet
  DATA_PATH: /root/lanyun-fs/imagenet100-split
  IMG_SIZE: 224
  INTERPOLATION: bicubic
  NUM_WORKERS: 8
  PIN_MEMORY: true
  ZIP_MODE: false
ENABLE_AMP: false
EVAL_MODE: false
FUSED_LAYERNORM: false
FUSED_WINDOW_PROCESS: false
LOCAL_RANK: 0
MODEL:
  DROP_PATH_RATE: 0.1
  DROP_RATE: 0.0
  LABEL_SMOOTHING: 0.1
  NAME: EnhancedvHeat_tiny_imagenet100
  NUM_CLASSES: 100
  PRETRAINED: ''
  RESUME: ''
  TYPE: EnhancedvHeat
  VHEAT:
    DEPTHS:
    - 2
    - 2
    - 6
    - 2
    EMBED_DIM: 96
    ENABLE_THERMAL_RADIATION: true
    IN_CHANS: 3
    LAYER_SCALE: null
    MLP_RATIO: 4.0
    PATCH_NORM: true
    PATCH_SIZE: 4
    POST_NORM: false
    RADIATION_STRENGTH: 0.05
OUTPUT: ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default
PRINT_FREQ: 10
SAVE_FREQ: 5
SEED: 0
TAG: default
TEST:
  CROP: true
  SEQUENTIAL: false
  SHUFFLE: false
THROUGHPUT_MODE: false
TRAIN:
  ACCUMULATION_STEPS: 1
  AUTO_RESUME: true
  BASE_LR: 0.00025
  CLIP_GRAD: 5.0
  EPOCHS: 100
  LAYER_DECAY: 1.0
  LR_SCHEDULER:
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    GAMMA: 0.1
    MULTISTEPS: []
    NAME: cosine
    WARMUP_PREFIX: true
  MIN_LR: 2.5e-06
  MOE:
    SAVE_MASTER: false
  OPTIMIZER:
    BETAS:
    - 0.9
    - 0.999
    EPS: 1.0e-08
    MOMENTUM: 0.9
    NAME: adamw
  START_EPOCH: 0
  USE_CHECKPOINT: false
  WARMUP_EPOCHS: 20
  WARMUP_LR: 2.5e-07
  WEIGHT_DECAY: 0.08

[2025-08-26 20:47:56 EnhancedvHeat_tiny_imagenet100] (main.py 471): INFO {"cfg": "classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml", "opts": ["TRAIN.EPOCHS", "100", "MODEL.NUM_CLASSES", "100"], "batch_size": 256, "data_path": "/root/lanyun-fs/imagenet100-split", "zip": false, "cache_mode": "part", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "./output_enhanced_tiny", "tag": null, "eval": false, "throughput": false, "local_rank": 0, "fused_layernorm": false, "optim": null, "model_ema": true, "model_ema_decay": 0.9999, "model_ema_force_cpu": false}
[2025-08-26 20:47:56 EnhancedvHeat_tiny_imagenet100] (main.py 117): INFO Creating model:EnhancedvHeat/EnhancedvHeat_tiny_imagenet100
[2025-08-26 20:48:05 EnhancedvHeat_tiny_imagenet100] (main.py 122): INFO N/A indicates a possibly missing statistic due to how the module was called. Missing values are still included in the parent's total.
EnhancedvHeat(
  #params: 33.41M, #flops: 4.94G
  (patch_embed): StemLayer(
    #params: 43.2K, #flops: 0.15G
    (conv1): Conv2d(
      3, 48, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1)
      #params: 1.34K, #flops: 16.26M
    )
    (norm1): Sequential(
      #params: 96, #flops: 3.01M
      (0): to_channels_last()
      (1): LayerNorm(
        (48,), eps=1e-06, elementwise_affine=True
        #params: 96, #flops: 3.01M
      )
      (2): to_channels_first()
    )
    (act): GELU(approximate='none')
    (conv2): Conv2d(
      48, 96, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1)
      #params: 41.57K, #flops: 0.13G
    )
    (norm2): Sequential(
      #params: 0.19K, #flops: 1.51M
      (0): to_channels_last()
      (1): LayerNorm(
        (96,), eps=1e-06, elementwise_affine=True
        #params: 0.19K, #flops: 1.51M
      )
      (2): to_channels_first()
    )
  )
  (freq_embed): ParameterList(
      (0): Parameter containing: [torch.float32 of size 56x56x96]
      (1): Parameter containing: [torch.float32 of size 28x28x192]
      (2): Parameter containing: [torch.float32 of size 14x14x384]
      (3): Parameter containing: [torch.float32 of size 7x7x768]
    #params: 0.56M, #flops: N/A
  )
  (layers): ModuleList(
    #params: 32.72M, #flops: 4.78G
    (0): AdditionalInputSequential(
      #params: 0.42M, #flops: 0.97G
      (0): EnhancedHeatBlock(
        #params: 0.13M, #flops: 0.42G
        (norm1): LayerNorm2d(
          (96,), eps=1e-05, elementwise_affine=True
          #params: 0.19K, #flops: 1.51M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 38.4K, #flops: 0.19G
          (dwconv): Conv2d(
            96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=96
            #params: 0.96K, #flops: 2.71M
          )
          (linear): Linear(
            in_features=96, out_features=192, bias=True
            #params: 18.62K, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (96,), eps=1e-05, elementwise_affine=True
            #params: 0.19K, #flops: 1.51M
          )
          (out_linear): Linear(
            in_features=96, out_features=96, bias=True
            #params: 9.31K, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 9.31K, #flops: 28.9M
            (0): Linear(
              in_features=96, out_features=96, bias=True
              #params: 9.31K, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=96)
          (linear): Linear(in_features=96, out_features=192, bias=True)
          (out_norm): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=96, out_features=96, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=96, out_features=96, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 14.04K, #flops: N/A
          (channel_interaction): Linear(
            in_features=96, out_features=96, bias=False
            #params: 9.22K, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 4.73K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              96, 24, kernel_size=(1, 1), stride=(1, 1)
              #params: 2.33K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              24, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 2.4K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): Identity(#params: 0, #flops: N/A)
        (norm2): LayerNorm2d(
          (96,), eps=1e-05, elementwise_affine=True
          #params: 0.19K, #flops: 1.51M
        )
        (mlp): Mlp(
          #params: 74.21K, #flops: 0.23G
          (fc1): Conv2d(
            96, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 37.25K, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            384, 96, kernel_size=(1, 1), stride=(1, 1)
            #params: 36.96K, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        #params: 0.13M, #flops: 0.42G
        (norm1): LayerNorm2d(
          (96,), eps=1e-05, elementwise_affine=True
          #params: 0.19K, #flops: 1.51M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 38.4K, #flops: 0.19G
          (dwconv): Conv2d(
            96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=96
            #params: 0.96K, #flops: 2.71M
          )
          (linear): Linear(
            in_features=96, out_features=192, bias=True
            #params: 18.62K, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (96,), eps=1e-05, elementwise_affine=True
            #params: 0.19K, #flops: 1.51M
          )
          (out_linear): Linear(
            in_features=96, out_features=96, bias=True
            #params: 9.31K, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 9.31K, #flops: 28.9M
            (0): Linear(
              in_features=96, out_features=96, bias=True
              #params: 9.31K, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(96, 96, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=96)
          (linear): Linear(in_features=96, out_features=192, bias=True)
          (out_norm): LayerNorm((96,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=96, out_features=96, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=96, out_features=96, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 14.04K, #flops: N/A
          (channel_interaction): Linear(
            in_features=96, out_features=96, bias=False
            #params: 9.22K, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 4.73K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              96, 24, kernel_size=(1, 1), stride=(1, 1)
              #params: 2.33K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              24, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 2.4K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (96,), eps=1e-05, elementwise_affine=True
          #params: 0.19K, #flops: 1.51M
        )
        (mlp): Mlp(
          #params: 74.21K, #flops: 0.23G
          (fc1): Conv2d(
            96, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 37.25K, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            384, 96, kernel_size=(1, 1), stride=(1, 1)
            #params: 36.96K, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): Sequential(
        #params: 0.17M, #flops: 0.13G
        (0): Conv2d(
          96, 192, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False
          #params: 0.17M, #flops: 0.13G
        )
        (1): LayerNorm2d(
          (192,), eps=1e-05, elementwise_affine=True
          #params: 0.38K, #flops: 0.75M
        )
      )
    )
    (1): AdditionalInputSequential(
      #params: 1.67M, #flops: 0.87G
      (0): EnhancedHeatBlock(
        #params: 0.5M, #flops: 0.37G
        (norm1): LayerNorm2d(
          (192,), eps=1e-05, elementwise_affine=True
          #params: 0.38K, #flops: 0.75M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.15M, #flops: 0.13G
          (dwconv): Conv2d(
            192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192
            #params: 1.92K, #flops: 1.35M
          )
          (linear): Linear(
            in_features=192, out_features=384, bias=True
            #params: 74.11K, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (192,), eps=1e-05, elementwise_affine=True
            #params: 0.38K, #flops: 0.75M
          )
          (out_linear): Linear(
            in_features=192, out_features=192, bias=True
            #params: 37.06K, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 37.06K, #flops: 28.9M
            (0): Linear(
              in_features=192, out_features=192, bias=True
              #params: 37.06K, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192)
          (linear): Linear(in_features=192, out_features=384, bias=True)
          (out_norm): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=192, out_features=192, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=192, out_features=192, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 55.73K, #flops: N/A
          (channel_interaction): Linear(
            in_features=192, out_features=192, bias=False
            #params: 36.86K, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 18.67K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              192, 48, kernel_size=(1, 1), stride=(1, 1)
              #params: 9.26K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              48, 192, kernel_size=(1, 1), stride=(1, 1)
              #params: 9.41K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (192,), eps=1e-05, elementwise_affine=True
          #params: 0.38K, #flops: 0.75M
        )
        (mlp): Mlp(
          #params: 0.3M, #flops: 0.23G
          (fc1): Conv2d(
            192, 768, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.15M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            768, 192, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.15M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        #params: 0.5M, #flops: 0.37G
        (norm1): LayerNorm2d(
          (192,), eps=1e-05, elementwise_affine=True
          #params: 0.38K, #flops: 0.75M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.15M, #flops: 0.13G
          (dwconv): Conv2d(
            192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192
            #params: 1.92K, #flops: 1.35M
          )
          (linear): Linear(
            in_features=192, out_features=384, bias=True
            #params: 74.11K, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (192,), eps=1e-05, elementwise_affine=True
            #params: 0.38K, #flops: 0.75M
          )
          (out_linear): Linear(
            in_features=192, out_features=192, bias=True
            #params: 37.06K, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 37.06K, #flops: 28.9M
            (0): Linear(
              in_features=192, out_features=192, bias=True
              #params: 37.06K, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(192, 192, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=192)
          (linear): Linear(in_features=192, out_features=384, bias=True)
          (out_norm): LayerNorm((192,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=192, out_features=192, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=192, out_features=192, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 55.73K, #flops: N/A
          (channel_interaction): Linear(
            in_features=192, out_features=192, bias=False
            #params: 36.86K, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 18.67K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              192, 48, kernel_size=(1, 1), stride=(1, 1)
              #params: 9.26K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              48, 192, kernel_size=(1, 1), stride=(1, 1)
              #params: 9.41K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (192,), eps=1e-05, elementwise_affine=True
          #params: 0.38K, #flops: 0.75M
        )
        (mlp): Mlp(
          #params: 0.3M, #flops: 0.23G
          (fc1): Conv2d(
            192, 768, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.15M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            768, 192, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.15M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): Sequential(
        #params: 0.66M, #flops: 0.13G
        (0): Conv2d(
          192, 384, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False
          #params: 0.66M, #flops: 0.13G
        )
        (1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
      )
    )
    (2): AdditionalInputSequential(
      #params: 14.66M, #flops: 2.25G
      (0): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (3): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (4): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (5): EnhancedHeatBlock(
        #params: 2M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 0.6M, #flops: 0.12G
          (dwconv): Conv2d(
            384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384
            #params: 3.84K, #flops: 0.68M
          )
          (linear): Linear(
            in_features=384, out_features=768, bias=True
            #params: 0.3M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (384,), eps=1e-05, elementwise_affine=True
            #params: 0.77K, #flops: 0.38M
          )
          (out_linear): Linear(
            in_features=384, out_features=384, bias=True
            #params: 0.15M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.15M, #flops: 28.9M
            (0): Linear(
              in_features=384, out_features=384, bias=True
              #params: 0.15M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(384, 384, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=384)
          (linear): Linear(in_features=384, out_features=768, bias=True)
          (out_norm): LayerNorm((384,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=384, out_features=384, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=384, out_features=384, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.22M, #flops: N/A
          (channel_interaction): Linear(
            in_features=384, out_features=384, bias=False
            #params: 0.15M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 74.21K, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              384, 96, kernel_size=(1, 1), stride=(1, 1)
              #params: 36.96K, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              96, 384, kernel_size=(1, 1), stride=(1, 1)
              #params: 37.25K, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (384,), eps=1e-05, elementwise_affine=True
          #params: 0.77K, #flops: 0.38M
        )
        (mlp): Mlp(
          #params: 1.18M, #flops: 0.23G
          (fc1): Conv2d(
            384, 1536, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            1536, 384, kernel_size=(1, 1), stride=(1, 1)
            #params: 0.59M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (6): Sequential(
        #params: 2.66M, #flops: 0.13G
        (0): Conv2d(
          384, 768, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False
          #params: 2.65M, #flops: 0.13G
        )
        (1): LayerNorm2d(
          (768,), eps=1e-05, elementwise_affine=True
          #params: 1.54K, #flops: 0.19M
        )
      )
    )
    (3): AdditionalInputSequential(
      #params: 15.97M, #flops: 0.7G
      (0): EnhancedHeatBlock(
        #params: 7.98M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (768,), eps=1e-05, elementwise_affine=True
          #params: 1.54K, #flops: 0.19M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 2.37M, #flops: 0.12G
          (dwconv): Conv2d(
            768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=768
            #params: 7.68K, #flops: 0.34M
          )
          (linear): Linear(
            in_features=768, out_features=1536, bias=True
            #params: 1.18M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (768,), eps=1e-05, elementwise_affine=True
            #params: 1.54K, #flops: 0.19M
          )
          (out_linear): Linear(
            in_features=768, out_features=768, bias=True
            #params: 0.59M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.59M, #flops: 28.9M
            (0): Linear(
              in_features=768, out_features=768, bias=True
              #params: 0.59M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=768)
          (linear): Linear(in_features=768, out_features=1536, bias=True)
          (out_norm): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=768, out_features=768, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=768, out_features=768, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.89M, #flops: N/A
          (channel_interaction): Linear(
            in_features=768, out_features=768, bias=False
            #params: 0.59M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 0.3M, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              768, 192, kernel_size=(1, 1), stride=(1, 1)
              #params: 0.15M, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              192, 768, kernel_size=(1, 1), stride=(1, 1)
              #params: 0.15M, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (768,), eps=1e-05, elementwise_affine=True
          #params: 1.54K, #flops: 0.19M
        )
        (mlp): Mlp(
          #params: 4.72M, #flops: 0.23G
          (fc1): Conv2d(
            768, 3072, kernel_size=(1, 1), stride=(1, 1)
            #params: 2.36M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            3072, 768, kernel_size=(1, 1), stride=(1, 1)
            #params: 2.36M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (1): EnhancedHeatBlock(
        #params: 7.98M, #flops: 0.35G
        (norm1): LayerNorm2d(
          (768,), eps=1e-05, elementwise_affine=True
          #params: 1.54K, #flops: 0.19M
        )
        (spatial_heat_conduction): Heat2D(
          #params: 2.37M, #flops: 0.12G
          (dwconv): Conv2d(
            768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=768
            #params: 7.68K, #flops: 0.34M
          )
          (linear): Linear(
            in_features=768, out_features=1536, bias=True
            #params: 1.18M, #flops: 57.8M
          )
          (out_norm): LayerNorm(
            (768,), eps=1e-05, elementwise_affine=True
            #params: 1.54K, #flops: 0.19M
          )
          (out_linear): Linear(
            in_features=768, out_features=768, bias=True
            #params: 0.59M, #flops: 28.9M
          )
          (to_k): Sequential(
            #params: 0.59M, #flops: 28.9M
            (0): Linear(
              in_features=768, out_features=768, bias=True
              #params: 0.59M, #flops: 28.9M
            )
            (1): ReLU()
          )
        )
        (op): Heat2D(
          (dwconv): Conv2d(768, 768, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), groups=768)
          (linear): Linear(in_features=768, out_features=1536, bias=True)
          (out_norm): LayerNorm((768,), eps=1e-05, elementwise_affine=True)
          (out_linear): Linear(in_features=768, out_features=768, bias=True)
          (to_k): Sequential(
            (0): Linear(in_features=768, out_features=768, bias=True)
            (1): ReLU()
          )
        )
        (channel_thermal_radiation): ThermalRadiationModule(
          #params: 0.89M, #flops: N/A
          (channel_interaction): Linear(
            in_features=768, out_features=768, bias=False
            #params: 0.59M, #flops: N/A
          )
          (temperature_predictor): Sequential(
            #params: 0.3M, #flops: N/A
            (0): AdaptiveAvgPool2d(
              output_size=1
              #params: 0, #flops: N/A
            )
            (1): Conv2d(
              768, 192, kernel_size=(1, 1), stride=(1, 1)
              #params: 0.15M, #flops: N/A
            )
            (2): ReLU(#params: 0, #flops: N/A)
            (3): Conv2d(
              192, 768, kernel_size=(1, 1), stride=(1, 1)
              #params: 0.15M, #flops: N/A
            )
            (4): Sigmoid(#params: 0, #flops: N/A)
          )
        )
        (drop_path): DropPath()
        (norm2): LayerNorm2d(
          (768,), eps=1e-05, elementwise_affine=True
          #params: 1.54K, #flops: 0.19M
        )
        (mlp): Mlp(
          #params: 4.72M, #flops: 0.23G
          (fc1): Conv2d(
            768, 3072, kernel_size=(1, 1), stride=(1, 1)
            #params: 2.36M, #flops: 0.12G
          )
          (act): GELU(approximate='none')
          (fc2): Conv2d(
            3072, 768, kernel_size=(1, 1), stride=(1, 1)
            #params: 2.36M, #flops: 0.12G
          )
          (drop): Dropout(p=0.0, inplace=False)
        )
      )
      (2): Identity(#params: 0, #flops: N/A)
    )
  )
  (classifier): Sequential(
    #params: 78.44K, #flops: 0.3M
    (0): LayerNorm2d(
      (768,), eps=1e-05, elementwise_affine=True
      #params: 1.54K, #flops: 0.19M
    )
    (1): AdaptiveAvgPool2d(
      output_size=1
      #params: 0, #flops: 37.63K
    )
    (2): Flatten(start_dim=1, end_dim=-1)
    (3): Linear(
      in_features=768, out_features=100, bias=True
      #params: 76.9K, #flops: 76.8K
    )
  )
)
[2025-08-26 20:48:06 EnhancedvHeat_tiny_imagenet100] (main.py 175): INFO no checkpoint found in ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default, ignoring auto resume
[2025-08-26 20:48:06 EnhancedvHeat_tiny_imagenet100] (main.py 215): INFO Start training
[2025-08-26 20:48:17 EnhancedvHeat_tiny_imagenet100] (main.py 317): INFO Train: [0/100][0/507]	eta 1:33:08 lr 0.000000	 wd 0.0800	time 11.0230 (11.0230)	data time 5.8422 (5.8422)	loss 4.6753 (4.6753)	grad_norm 3.4124 (3.4124)	loss_scale 65536.0000 (65536.0000)	mem 22937MB
[2025-08-26 20:53:37 EnhancedvHeat_tiny_imagenet100] (main.py 479): INFO Full config saved to ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default/config.json
[2025-08-26 20:53:37 EnhancedvHeat_tiny_imagenet100] (main.py 482): INFO AMP_ENABLE: true
AMP_OPT_LEVEL: ''
AUG:
  AUTO_AUGMENT: rand-m9-mstd0.5-inc1
  COLOR_JITTER: 0.4
  CUTMIX: 1.0
  CUTMIX_MINMAX: null
  MIXUP: 0.8
  MIXUP_MODE: batch
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  RECOUNT: 1
  REMODE: pixel
  REPROB: 0.25
BASE:
- ''
DATA:
  BATCH_SIZE: 256
  CACHE_MODE: part
  DATASET: imagenet
  DATA_PATH: /root/lanyun-fs/imagenet100-split
  IMG_SIZE: 224
  INTERPOLATION: bicubic
  NUM_WORKERS: 8
  PIN_MEMORY: true
  ZIP_MODE: false
ENABLE_AMP: false
EVAL_MODE: false
FUSED_LAYERNORM: false
FUSED_WINDOW_PROCESS: false
LOCAL_RANK: 0
MODEL:
  DROP_PATH_RATE: 0.1
  DROP_RATE: 0.0
  LABEL_SMOOTHING: 0.1
  NAME: EnhancedvHeat_tiny_imagenet100
  NUM_CLASSES: 100
  PRETRAINED: ''
  RESUME: ''
  TYPE: EnhancedvHeat
  VHEAT:
    DEPTHS:
    - 2
    - 2
    - 6
    - 2
    EMBED_DIM: 96
    ENABLE_THERMAL_RADIATION: true
    IN_CHANS: 3
    LAYER_SCALE: null
    MLP_RATIO: 4.0
    PATCH_NORM: true
    PATCH_SIZE: 4
    POST_NORM: false
    RADIATION_STRENGTH: 0.05
OUTPUT: ./output_enhanced_tiny/EnhancedvHeat_tiny_imagenet100/default
PRINT_FREQ: 10
SAVE_FREQ: 5
SEED: 0
TAG: default
TEST:
  CROP: true
  SEQUENTIAL: false
  SHUFFLE: false
THROUGHPUT_MODE: false
TRAIN:
  ACCUMULATION_STEPS: 1
  AUTO_RESUME: true
  BASE_LR: 0.00025
  CLIP_GRAD: 5.0
  EPOCHS: 100
  LAYER_DECAY: 1.0
  LR_SCHEDULER:
    DECAY_EPOCHS: 30
    DECAY_RATE: 0.1
    GAMMA: 0.1
    MULTISTEPS: []
    NAME: cosine
    WARMUP_PREFIX: true
  MIN_LR: 2.5e-06
  MOE:
    SAVE_MASTER: false
  OPTIMIZER:
    BETAS:
    - 0.9
    - 0.999
    EPS: 1.0e-08
    MOMENTUM: 0.9
    NAME: adamw
  START_EPOCH: 0
  USE_CHECKPOINT: false
  WARMUP_EPOCHS: 20
  WARMUP_LR: 2.5e-07
  WEIGHT_DECAY: 0.08

[2025-08-26 20:53:37 EnhancedvHeat_tiny_imagenet100] (main.py 483): INFO {"cfg": "classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml", "opts": ["TRAIN.EPOCHS", "100", "MODEL.NUM_CLASSES", "100"], "batch_size": 256, "data_path": "/root/lanyun-fs/imagenet100-split", "zip": false, "cache_mode": "part", "pretrained": null, "resume": null, "accumulation_steps": null, "use_checkpoint": false, "disable_amp": false, "amp_opt_level": null, "output": "./output_enhanced_tiny", "tag": null, "eval": false, "throughput": false, "local_rank": 0, "fused_layernorm": false, "optim": null, "model_ema": true, "model_ema_decay": 0.9999, "model_ema_force_cpu": false}
